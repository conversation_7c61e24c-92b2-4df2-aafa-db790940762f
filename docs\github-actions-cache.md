# GitHub Actions 缓存配置

本文档描述了项目中 GitHub Actions 工作流的缓存配置，用于提高 CI/CD 流水线的执行效率。

## 缓存策略概述

我们的 GitHub Actions 工作流使用了多层缓存策略来优化构建时间：

### 1. 前端构建缓存 (format-build-frontend.yaml)

#### Node.js 依赖缓存
- **setup-node 内置缓存**: 使用 `cache: 'npm'` 参数自动缓存 npm 全局缓存
- **node_modules 缓存**: 缓存整个 `node_modules` 目录
  - 缓存键: `${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json') }}`
  - 恢复键: `${{ runner.os }}-node-modules-`

#### Vite 构建缓存
- **Vite 缓存目录**: 缓存 Vite 的构建缓存
  - 路径: `.vite` 和 `node_modules/.vite`
  - 缓存键: `${{ runner.os }}-vite-${{ hashFiles('vite.config.js', 'vite.config.ts', 'package-lock.json') }}`
  - 恢复键: `${{ runner.os }}-vite-`

### 2. 后端格式化缓存 (format-backend.yaml)

#### Python 依赖缓存
- **setup-python 内置缓存**: 使用 `cache: 'pip'` 参数自动缓存 pip 缓存
- **pip 缓存目录**: 缓存 pip 下载的包
  - 路径: `~/.cache/pip`
  - 缓存键: `${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('**/requirements*.txt', 'pyproject.toml') }}`
  - 恢复键: `${{ runner.os }}-pip-${{ matrix.python-version }}-`, `${{ runner.os }}-pip-`

### 3. Docker 构建缓存 (docker-build.yaml)

#### Registry 缓存
- **Docker 层缓存**: 使用 Docker registry 作为缓存后端
- **缓存策略**: `cache-from` 和 `cache-to` 配置
- **缓存模式**: `mode=max` 最大化缓存效果

## 缓存键策略

### 缓存键设计原则
1. **操作系统**: 包含 `${{ runner.os }}` 确保不同操作系统的缓存隔离
2. **依赖文件哈希**: 使用 `hashFiles()` 函数基于依赖文件内容生成缓存键
3. **版本信息**: 包含相关版本信息（如 Python 版本）确保兼容性

### 恢复键策略
使用多级恢复键确保在精确匹配失败时仍能利用部分缓存：
1. 精确匹配：完整的缓存键
2. 部分匹配：去掉最具体的部分（如文件哈希）
3. 通用匹配：只保留操作系统和工具类型

## 性能优化效果

### 预期改进
- **首次运行**: 无缓存，正常构建时间
- **依赖未变更**: 显著减少依赖安装时间（通常减少 70-90%）
- **增量构建**: Vite 缓存可加速重复构建
- **Docker 构建**: 层缓存可大幅减少镜像构建时间

### 缓存命中率优化
1. **细粒度缓存键**: 基于实际依赖文件内容，避免不必要的缓存失效
2. **多级恢复**: 即使精确匹配失败，仍能利用部分缓存
3. **合理的缓存路径**: 缓存最有价值的目录和文件

## 维护和监控

### 缓存监控
- 在 GitHub Actions 日志中查看缓存命中/未命中信息
- 监控构建时间变化趋势
- 定期检查缓存大小和存储使用情况

### 缓存清理
- GitHub Actions 自动管理缓存生命周期
- 缓存在 7 天未使用后自动删除
- 每个仓库有 10GB 的缓存存储限制

### 故障排除
如果遇到缓存相关问题：
1. 检查缓存键是否正确生成
2. 验证缓存路径是否存在
3. 考虑手动清除缓存（通过更改缓存键）
4. 检查依赖文件是否正确包含在缓存键中

## 最佳实践

1. **缓存键设计**: 包含所有影响缓存内容的因素
2. **路径选择**: 只缓存真正需要的目录，避免缓存过大
3. **恢复键**: 设置合理的恢复键层次结构
4. **监控**: 定期检查缓存效果和构建时间
5. **更新**: 随着项目依赖变化及时调整缓存策略
