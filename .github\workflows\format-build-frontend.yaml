name: Frontend Build

on:
  push:
    branches:
      - main
      - dev
    paths-ignore:
      - 'backend/**'
      - 'pyproject.toml'
      - 'uv.lock'
  pull_request:
    branches:
      - main
      - dev
    paths-ignore:
      - 'backend/**'
      - 'pyproject.toml'
      - 'uv.lock'

jobs:
  build:
    name: 'Format & Build Frontend'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Cache Vite build cache
        uses: actions/cache@v4
        with:
          path: |
            .vite
            node_modules/.vite
          key: ${{ runner.os }}-vite-${{ hashFiles('vite.config.js', 'vite.config.ts', 'package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-vite-

      - name: Install Dependencies
        run: npm ci

      - name: Format Frontend
        run: npm run format

      - name: Run i18next
        run: npm run i18n:parse

      - name: Check for Changes After Format
        run: git diff --exit-code

      - name: Build Frontend
        run: npm run build

  test-frontend:
    name: 'Frontend Unit Tests'
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '22'
          cache: 'npm'

      - name: Cache node_modules
        uses: actions/cache@v4
        with:
          path: node_modules
          key: ${{ runner.os }}-node-modules-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-modules-

      - name: Cache Vite build cache
        uses: actions/cache@v4
        with:
          path: |
            .vite
            node_modules/.vite
          key: ${{ runner.os }}-vite-${{ hashFiles('vite.config.js', 'vite.config.ts', 'package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-vite-

      - name: Install Dependencies
        run: npm ci

      - name: Run vitest
        run: npm run test:frontend
