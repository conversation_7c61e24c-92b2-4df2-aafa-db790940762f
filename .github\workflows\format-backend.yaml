name: Python CI

on:
  push:
    branches:
      - main
      - dev
    paths:
      - 'backend/**'
      - 'pyproject.toml'
      - 'uv.lock'
  pull_request:
    branches:
      - main
      - dev
    paths:
      - 'backend/**'
      - 'pyproject.toml'
      - 'uv.lock'

jobs:
  build:
    name: 'Format Backend'
    runs-on: ubuntu-latest

    strategy:
      matrix:
        python-version:
          - 3.11.x
          - 3.12.x

    steps:
      - uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '${{ matrix.python-version }}'
          cache: 'pip'

      - name: Cache pip dependencies
        uses: actions/cache@v4
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ matrix.python-version }}-${{ hashFiles('**/requirements*.txt', 'pyproject.toml') }}
          restore-keys: |
            ${{ runner.os }}-pip-${{ matrix.python-version }}-
            ${{ runner.os }}-pip-

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install black

      - name: Format backend
        run: npm run format:backend

      - name: Check for changes after format
        run: git diff --exit-code
